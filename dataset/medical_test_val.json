[{"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010281\", \"姓名\": \"李学典\", \"年龄\": \"73\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月27日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1550.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1550.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008893\", \"姓名\": \"于佳妮\", \"年龄\": \"44\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月14日\", \"采集时间\": \"07:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1551.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1551.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009383\", \"姓名\": \"吕燕平\", \"年龄\": \"57\", \"性别\": \"女\", \"样本类型\": \"痰\", \"日期\": \"2024年11月1日\", \"采集时间\": \"07:10\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药前\"}], \"图片地址\": \"单张image_1552.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1552.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008910\", \"姓名\": \"张小平\", \"年龄\": \"44\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月14日\", \"采集时间\": \"12:20\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1553.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1553.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008912\", \"姓名\": \"赵廷治\", \"年龄\": \"17\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月14日\", \"采集时间\": \"12:20\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1554.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1554.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008045\", \"姓名\": \"张玉坦\", \"年龄\": \"68\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月19日\", \"采集时间\": \"07:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"HIV-Ab 呼吸道病毒五项 TB-Ab 风湿三项 甲状腺功能7项 抗核抗体十项 免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1555.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1555.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009752\", \"姓名\": \"李吉哪\", \"年龄\": \"16\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月10日\", \"采集时间\": \"11:20\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"HIV-Ab 呼吸道病毒五项 TB-Ab 风湿三项（ASO RF）抗核抗体十项\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1556.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1556.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008369\", \"姓名\": \"张方文\", \"年龄\": \"75\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月29日\", \"采集时间\": \"11:15\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1557.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1557.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009932\", \"姓名\": \"李浩宇\", \"年龄\": \"16\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月16日\", \"采集时间\": \"09:06\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM实验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1558.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1558.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005687\", \"姓名\": \"桑希生\", \"年龄\": \"61\", \"性别\": \"男\", \"样本类型\": \"肺泡灌洗液\", \"日期\": \"24.10.15\", \"采集时间\": \"12:02\", \"医生\": \"汪霞\", \"科室\": \"重症医学二病区\", \"检测项目\": \"病原靶向测序DNA+RNA周一-周六 \", \"备注栏\": NaN}], \"图片地址\": \"单张image_1559.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1559.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253003613\", \"姓名\": \"顿文忠\", \"年龄\": \"76\", \"性别\": \"男\", \"样本类型\": \"骨髓活检组织\", \"日期\": \"24.10.21\", \"采集时间\": \"11:27\", \"医生\": \"张晨\", \"科室\": \"血液内二病区\", \"检测项目\": \"骨髓活检+免疫组化10项\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1560.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1560.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005672\", \"姓名\": \"耿晨曦\", \"年龄\": \"5\", \"性别\": \"男\", \"样本类型\": \"肺泡灌洗液\", \"日期\": \"24.10.14\", \"采集时间\": \"17:37\", \"医生\": \"牛祖西\", \"科室\": \"重症医学一病区\", \"检测项目\": \"病原靶向测序DNA+RNA周一-周六 \", \"备注栏\": NaN}], \"图片地址\": \"单张image_1561.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1561.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005673\", \"姓名\": \"刘乃润\", \"年龄\": \"6\", \"性别\": \"男\", \"样本类型\": \"脑脊液\", \"日期\": \"24.10.14\", \"采集时间\": \"16:25\", \"医生\": \"王会松\", \"科室\": \"儿科四病区\", \"检测项目\": \"病原靶向测序DNA+RNA周一-周六 \", \"备注栏\": NaN}], \"图片地址\": \"单张image_1562.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1562.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253003898\", \"姓名\": \"邵珠云\", \"年龄\": \"61\", \"性别\": \"女\", \"样本类型\": \"骨髓活检组织\", \"日期\": \"24.10.28\", \"采集时间\": \"09:45\", \"医生\": \"关建民\", \"科室\": \"血液内一病区\", \"检测项目\": \"骨髓活检+免疫组化10项\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1563.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1563.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007458\", \"姓名\": \"周晨\", \"年龄\": \"37\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年10月01日\", \"采集时间\": \"11:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1564.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1564.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0115028386\", \"姓名\": \"袁雯琪\", \"年龄\": \"9\", \"性别\": \"女\", \"样本类型\": \"咽拭子\", \"日期\": \"2024-11-30\", \"采集时间\": \"9:00\", \"医生\": \"李明明\", \"科室\": \"儿科病区\", \"检测项目\": \"hxd20\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1565.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1565.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008004\", \"姓名\": \"胡旺峰\", \"年龄\": \"16\", \"性别\": \"男\", \"样本类型\": \"痰\", \"日期\": \"2024年11月18日\", \"采集时间\": \"08:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药前\"}], \"图片地址\": \"单张image_1566.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1566.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008798\", \"姓名\": \"龙云清\", \"年龄\": \"69\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月12日\", \"采集时间\": \"07:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"HIV-Ab，呼吸道病毒五项，TB-Ab，ASO，RF，甲状腺功能7项，免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1567.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1567.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007492\", \"姓名\": \"刘文琪\", \"年龄\": \"25\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年10月2日\", \"采集时间\": \"7:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1568.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1568.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008052\", \"姓名\": \"董光青\", \"年龄\": \"49\", \"性别\": \"男\", \"样本类型\": \"全血\", \"日期\": \"2024年11月19日\", \"采集时间\": \"09:50\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1569.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1569.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253006308\", \"姓名\": \"徐少伟\", \"年龄\": \"36\", \"性别\": \"男\", \"样本类型\": \"肺泡灌洗液\", \"日期\": \"24.10.31\", \"采集时间\": \"16:18\", \"医生\": \"姚红方\", \"科室\": \"呼1\", \"检测项目\": \"病原靶向测序tNGS（DNA+RNA）（自开）\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1570.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1570.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009839\", \"姓名\": \"孙文武\", \"年龄\": \"50\", \"性别\": \"男\", \"样本类型\": \"全血\", \"日期\": \"2024年11月12日\", \"采集时间\": \"10:57\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1571.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1571.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008056\", \"姓名\": \"郭安洪\", \"年龄\": \"71\", \"性别\": \"男\", \"样本类型\": \"全血\", \"日期\": \"2024年11月19日\", \"采集时间\": \"11:20\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1572.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1572.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005543\", \"姓名\": \"刘同云\", \"年龄\": \"79\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"24.10.12\", \"采集时间\": \"08:06\", \"医生\": \"胡凤利\", \"科室\": \"神经内科\", \"检测项目\": \"他克莫司药浓度检测\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1573.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1573.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007628\", \"姓名\": \"张学军\", \"年龄\": \"48\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年10月4日\", \"采集时间\": \"12:35\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1574.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1574.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008945\", \"姓名\": \"孙梅青\", \"年龄\": \"55\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月15日\", \"采集时间\": \"11:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1575.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1575.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010167\", \"姓名\": \"王顺利\", \"年龄\": \"71\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月23日\", \"采集时间\": \"09:26\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"HIV-Ab，呼吸道病毒五项，TB-Ab，风湿三项（ASO，CRP，RF），甲状腺功能7项，抗核抗体十项，免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1576.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1576.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010246\", \"姓名\": \"王利明\", \"年龄\": \"66\", \"性别\": \"男\", \"样本类型\": \"痰\", \"日期\": \"2024年12月26日\", \"采集时间\": \"8:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药后/限制级\"}], \"图片地址\": \"单张image_1577.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1577.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009937\", \"姓名\": \"张凡兰\", \"年龄\": \"56\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月16日\", \"采集时间\": \"11:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM实验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1578.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1578.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005047\", \"姓名\": \"高忠发\", \"年龄\": \"44\", \"性别\": \"男\", \"样本类型\": \"胸水\", \"日期\": \"24.10.01\", \"采集时间\": \"09:45\", \"医生\": \"吴凤娟\", \"科室\": \"呼吸与危重症一病区\", \"检测项目\": \"结核杆菌(TB-DNA)测定（胸)\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1579.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1579.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253006013\", \"姓名\": \"楚佳恩\", \"年龄\": \"5\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"24.10.23\", \"采集时间\": \"14:42\", \"医生\": \"蔡蕊\", \"科室\": \"小儿科\", \"检测项目\": \"百日咳杆菌IgM抗体\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1580.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1580.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0115028137\", \"姓名\": \"刘泽宇\", \"年龄\": \"6\", \"性别\": \"男\", \"样本类型\": \"咽拭子\", \"日期\": \"2024-11-17\", \"采集时间\": \"9:00\", \"医生\": \"徐继爱\", \"科室\": \"硕园儿科病区\", \"检测项目\": \"hxd20\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1581.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1581.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009555\", \"姓名\": \"刘鹏\", \"年龄\": \"39\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月5日\", \"采集时间\": \"07:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"HIV-Ab 呼吸道病毒五项 TB-Ab 风湿三项（ASO CRP RF） 甲状腺功能7项 免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1582.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1582.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009543\", \"姓名\": \"管金艳\", \"年龄\": \"47\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"2024年11月4日\", \"采集时间\": \"11:34\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1583.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1583.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008875\", \"姓名\": \"李言宝\", \"年龄\": \"53\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月13日\", \"采集时间\": \"11:45\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1584.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1584.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005641\", \"姓名\": \"宗继贤\", \"年龄\": \"75\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"24.10.14\", \"采集时间\": \"11:15\", \"医生\": \"曹福英\", \"科室\": \"血液内一病区\", \"检测项目\": \"促红细胞生成素（EPO）\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1585.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1585.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009936\", \"姓名\": \"张凡兰\", \"年龄\": \"56\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月16日\", \"采集时间\": \"11:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1586.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1586.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008268\", \"姓名\": \"兰洪波\", \"年龄\": \"60\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月26日\", \"采集时间\": \"07:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"呼吸道病毒五项 TB-Ab ASO RF 甲状腺功能7项 抗核抗体十项 免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1587.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1587.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007579\", \"姓名\": \"房桂花\", \"年龄\": \"68\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年10月3日\", \"采集时间\": \"11:30\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"HIV-Ab，呼吸道病毒五项，TB-Ab，ASO，RF\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1588.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1588.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010363\", \"姓名\": \"怀其华\", \"年龄\": \"43\", \"性别\": \"男\", \"样本类型\": \"痰\", \"日期\": \"2024年12月29日\", \"采集时间\": \"09:34\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药前\"}], \"图片地址\": \"单张image_1589.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1589.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005965\", \"姓名\": \"周凤丽\", \"年龄\": \"52\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"24.10.22\", \"采集时间\": \"11:36\", \"医生\": \"关建民\", \"科室\": \"血液内科\", \"检测项目\": \"环孢霉素A浓度\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1590.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1590.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008147\", \"姓名\": \"董元斌\", \"年龄\": \"57\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月22日\", \"采集时间\": \"09:21\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1591.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1591.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005699\", \"姓名\": \"王亭亭\", \"年龄\": \"37\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"24.10.16\", \"采集时间\": \"09:22\", \"医生\": \"智钦钦\", \"科室\": \"便民门诊\", \"检测项目\": \"外周血B细胞绝对计数检测\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1592.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1592.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008950\", \"姓名\": \"崔大远\", \"年龄\": \"54\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"2024年12月\", \"采集时间\": NaN, \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1593.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1593.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008633\", \"姓名\": \"李中信\", \"年龄\": \"58\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月07日\", \"采集时间\": \"11:30\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"HIV-Ab，呼吸道病毒五项，TB-Ab，抗核抗体十项，免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1594.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1594.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0115028049\", \"姓名\": \"张同海\", \"年龄\": \"68\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024-11-09\", \"采集时间\": \"8:00\", \"医生\": \"张志文\", \"科室\": \"内科病区\", \"检测项目\": \"HBV-DNA\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1595.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1595.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009771\", \"姓名\": \"张桂香\", \"年龄\": \"73\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月11日\", \"采集时间\": \"07:00\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1596.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1596.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005109\", \"姓名\": \"张风霞\", \"年龄\": \"59\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"24.10.02\", \"采集时间\": \"09:07\", \"医生\": \"祁建军\", \"科室\": \"腹膜透析门诊\", \"检测项目\": \"他克莫司药浓度检测\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1597.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1597.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010214\", \"姓名\": \"潘秀英\", \"年龄\": \"57\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月24日\", \"采集时间\": \"11:03\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1598.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1598.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008150\", \"姓名\": \"胡秋丽\", \"年龄\": \"63\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月22日\", \"采集时间\": \"10:44\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1599.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1599.jpg"]}]
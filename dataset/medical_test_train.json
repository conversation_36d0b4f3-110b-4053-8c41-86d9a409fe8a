[{"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009589\", \"姓名\": \"孟祥俭\", \"年龄\": \"64\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月06日\", \"采集时间\": \"07:00\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1500.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1500.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0115028027\", \"姓名\": \"高铭洋\", \"年龄\": \"5\", \"性别\": \"男\", \"样本类型\": \"咽拭子\", \"日期\": \"2024-11-11\", \"采集时间\": \"9:00\", \"医生\": \"李明明\", \"科室\": \"儿科病区\", \"检测项目\": \"hxd20\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1501.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1501.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": 491012028645, \"姓名\": \"张磊磊\", \"年龄\": \"35\", \"性别\": \"女\", \"样本类型\": \"分泌物\", \"日期\": \"5月3日\", \"采集时间\": \"9:00\", \"医生\": NaN, \"科室\": NaN, \"检测项目\": \"TCT\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1502.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1502.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008854\", \"姓名\": \"步素玲\", \"年龄\": \"37\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"2024年12月13日\", \"采集时间\": \"07:00\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1503.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1503.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010201\", \"姓名\": \"毛春红\", \"年龄\": \"42\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月24日\", \"采集时间\": \"07:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1504.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1504.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008254\", \"姓名\": \"郎文光\", \"年龄\": \"71\", \"性别\": \"男\", \"样本类型\": \"全血\", \"日期\": \"2024年11月25日\", \"采集时间\": \"08:52\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1505.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1505.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253003896\", \"姓名\": \"李玉保\", \"年龄\": \"52\", \"性别\": \"男\", \"样本类型\": \"骨髓活检组织\", \"日期\": \"24.10.28\", \"采集时间\": \"09:25\", \"医生\": \"关建民\", \"科室\": \"血液内一病区\", \"检测项目\": \"骨髓活检+免疫组化10项\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1506.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1506.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005728\", \"姓名\": \"李沐尘\", \"年龄\": \"3\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"24.10.16\", \"采集时间\": \"12:59\", \"医生\": \"王翠华\", \"科室\": \"小儿科\", \"检测项目\": \"百日咳杆菌IgM抗体，ELISA\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1507.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1507.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008591\", \"姓名\": \"刘汉岗\", \"年龄\": \"55\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月06日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1508.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1508.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008671\", \"姓名\": \"徐翠玲\", \"年龄\": \"58\", \"性别\": \"女\", \"样本类型\": \"痰\", \"日期\": \"2024年12月8日\", \"采集时间\": \"07:00\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药后/非限制/限制级\"}], \"图片地址\": \"单张image_1509.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1509.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009405\", \"姓名\": \"徐玉红\", \"年龄\": \"50\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月1日\", \"采集时间\": \"09:24\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1510.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1510.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009633\", \"姓名\": \"杨大敏\", \"年龄\": \"64\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月7日\", \"采集时间\": \"10:50\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1511.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1511.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008956\", \"姓名\": \"谷昭胜\", \"年龄\": \"68\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月16日\", \"采集时间\": \"07:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"HIV-Ab，呼吸道病毒五项，TB-Ab，免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1512.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1512.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009623\", \"姓名\": \"邹长平\", \"年龄\": \"68\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月7日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1513.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1513.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008716\", \"姓名\": \"徐在松\", \"年龄\": \"52\", \"性别\": \"男\", \"样本类型\": \"全血\", \"日期\": \"2024年12月9日\", \"采集时间\": \"12:10\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1514.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1514.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253006232\", \"姓名\": \"王爱平\", \"年龄\": \"44\", \"性别\": \"女\", \"样本类型\": \"尿液\", \"日期\": \"24.10.28\", \"采集时间\": \"19:46\", \"医生\": \"刘增福\", \"科室\": \"消化内一病区\", \"检测项目\": \"儿茶酚胺(尿液)6项\", \"备注栏\": \"24H尿量2000ML\"}], \"图片地址\": \"单张image_1515.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1515.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007504\", \"姓名\": \"李永粉\", \"年龄\": \"52\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年10月02日\", \"采集时间\": \"11:10\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"HIV-Ab，呼吸道病毒五项，TB-Ab，风湿三项（ASO，CRP，RF），甲状腺功能7项，抗核抗体八项，免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1516.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1516.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008436\", \"姓名\": \"汪世红\", \"年龄\": \"39\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月1日\", \"采集时间\": \"10:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1517.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1517.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0115028255\", \"姓名\": \"陈玉俊\", \"年龄\": \"63\", \"性别\": \"女\", \"样本类型\": \"组织\", \"日期\": \"2024-11-22\", \"采集时间\": \"14时50分\", \"医生\": \"李凤\", \"科室\": \"内科\", \"检测项目\": \"内镜组织活检检查与诊断\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1518.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1518.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008146\", \"姓名\": \"董元斌\", \"年龄\": \"57\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月22日\", \"采集时间\": \"09:21\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1519.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1519.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009891\", \"姓名\": \"段清芬\", \"年龄\": \"52\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"2024年11月14日\", \"采集时间\": \"11:35\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1520.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1520.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": 491012029212, \"姓名\": \"周璐\", \"年龄\": \"22\", \"性别\": \"女\", \"样本类型\": \"血浆\", \"日期\": \"25年7月6日\", \"采集时间\": \"12:50\", \"医生\": NaN, \"科室\": NaN, \"检测项目\": \"D2聚体\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1521.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1521.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008224\", \"姓名\": \"王伟\", \"年龄\": \"51\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月24日\", \"采集时间\": \"10:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1522.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1522.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008824\", \"姓名\": \"张振红\", \"年龄\": \"48\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月12日\", \"采集时间\": \"8:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1523.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1523.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005612\", \"姓名\": \"孟联营\", \"年龄\": \"68\", \"性别\": \"男\", \"样本类型\": \"尿液\", \"日期\": \"24.10.14\", \"采集时间\": \"08:46\", \"医生\": \"王玉姣\", \"科室\": \"便民门诊\", \"检测项目\": \"免疫固定电泳（尿）5项\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1524.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1524.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008960\", \"姓名\": \"田凤喜\", \"年龄\": \"53\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月16日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1525.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1525.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009654\", \"姓名\": \"徐铁子\", \"年龄\": \"64\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年11月8日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1526.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1526.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009450\", \"姓名\": \"何文婷\", \"年龄\": \"41\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月02日\", \"采集时间\": \"11:20\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1527.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1527.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0115028224\", \"姓名\": \"刘子安\", \"年龄\": \"3\", \"性别\": \"女\", \"样本类型\": \"咽拭子\", \"日期\": \"2024-11-20\", \"采集时间\": \"9:00\", \"医生\": \"李明明\", \"科室\": \"儿科病区\", \"检测项目\": \"hxd20\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1528.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1528.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009775\", \"姓名\": \"刘华\", \"年龄\": \"14\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月11日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1529.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1529.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253006066\", \"姓名\": \"刘存德\", \"年龄\": \"77\", \"性别\": \"男\", \"样本类型\": \"脑脊液\", \"日期\": \"24.10.25\", \"采集时间\": \"15:12\", \"医生\": \"王松\", \"科室\": \"神经内三病区\", \"检测项目\": \"自身免疫性脑炎12项（脑脊液）\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1530.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1530.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007476\", \"姓名\": \"杨秀华\", \"年龄\": \"78\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年10月2日\", \"采集时间\": \"8:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1531.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1531.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008882\", \"姓名\": \"田宜苏\", \"年龄\": \"72\", \"性别\": \"女\", \"样本类型\": \"痰\", \"日期\": \"2024年12月14日\", \"采集时间\": \"8:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药前\"}], \"图片地址\": \"单张image_1532.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1532.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008559\", \"姓名\": \"石金国\", \"年龄\": \"56\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月4日\", \"采集时间\": \"08:45\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1533.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1533.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005449\", \"姓名\": \"刘贵荣\", \"年龄\": \"62\", \"性别\": \"女\", \"样本类型\": \"脑脊液\", \"日期\": \"24年10月08日\", \"采集时间\": \"14:20\", \"医生\": \"李丽\", \"科室\": \"神经内四病区\", \"检测项目\": \"中枢神经脱髓鞘4项\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1534.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1534.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0115028359\", \"姓名\": \"张明君\", \"年龄\": \"58\", \"性别\": \"男\", \"样本类型\": \"咽拭子\", \"日期\": \"2024-11-28\", \"采集时间\": \"8:00\", \"医生\": \"王乾\", \"科室\": \"内科病区\", \"检测项目\": \"普通细菌培养+药敏(定量)\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1535.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1535.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008408\", \"姓名\": \"姜丽\", \"年龄\": \"61\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年11月30日\", \"采集时间\": \"15:30\", \"医生\": \"刘忠\", \"科室\": NaN, \"检测项目\": \"G试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1536.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1536.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953009770\", \"姓名\": \"商好真\", \"年龄\": \"63\", \"性别\": \"男\", \"样本类型\": \"痰液\", \"日期\": \"2024年11月11日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药后/限制级\"}], \"图片地址\": \"单张image_1537.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1537.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007456\", \"姓名\": \"孙玉香\", \"年龄\": \"62\", \"性别\": \"女\", \"样本类型\": \"全血\", \"日期\": \"2024年10月1日\", \"采集时间\": \"11:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1538.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1538.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253005820\", \"姓名\": \"刘继军\", \"年龄\": \"54\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"24.10.19\", \"采集时间\": \"05:00\", \"医生\": \"段春英\", \"科室\": \"血液内二病区\", \"检测项目\": \"可溶性白细胞介素-2受体\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1539.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1539.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010350\", \"姓名\": \"怀其华\", \"年龄\": \"43\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月29日\", \"采集时间\": \"07:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"HIV-Ab，呼吸道病毒五项，TB-Ab，风湿三项（ASO，CRP，RF），抗核抗体十项\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1540.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1540.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007646\", \"姓名\": \"牟君\", \"年龄\": \"57\", \"性别\": \"女\", \"样本类型\": \"痰\", \"日期\": \"2024年10月5日\", \"采集时间\": \"07:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药前\"}], \"图片地址\": \"单张image_1541.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1541.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008504\", \"姓名\": \"蒋国富\", \"年龄\": \"71\", \"性别\": \"男\", \"样本类型\": \"血清\", \"日期\": \"2024年12月3日\", \"采集时间\": \"8:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1542.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1542.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008127\", \"姓名\": \"林同岭\", \"年龄\": \"71\", \"性别\": \"男\", \"样本类型\": \"全血\", \"日期\": \"2024年11月21日\", \"采集时间\": \"08:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"糖化血红蛋白\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1543.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1543.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z1253003944\", \"姓名\": \"杨光\", \"年龄\": \"22\", \"性别\": \"男\", \"样本类型\": \"骨髓\", \"日期\": \"24.10.31\", \"采集时间\": \"09:51\", \"医生\": \"张晨\", \"科室\": \"血液内二病区\", \"检测项目\": \"骨髓细胞染色体核型分析,G+R\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1544.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1544.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953010147\", \"姓名\": \"肖云凤\", \"年龄\": \"55\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年12月22日\", \"采集时间\": \"09:00\", \"医生\": \"李国林\", \"科室\": NaN, \"检测项目\": \"呼吸道病毒五项，TB-Ab，ASO，RF，甲状腺功能7项，抗核抗体十项，免疫球蛋白全套\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1545.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1545.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": 491012028725, \"姓名\": \"李晶\", \"年龄\": \"48\", \"性别\": \"女\", \"样本类型\": \"分泌物\", \"日期\": \"25年5月10日\", \"采集时间\": \"14:20\", \"医生\": NaN, \"科室\": NaN, \"检测项目\": \"HPV25\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1546.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1546.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953008264\", \"姓名\": \"王万明\", \"年龄\": \"56\", \"性别\": \"男\", \"样本类型\": \"痰\", \"日期\": \"2024年11月26日\", \"采集时间\": \"07:00\", \"医生\": \"刘庆龙\", \"科室\": NaN, \"检测项目\": \"痰培养+药敏\", \"备注栏\": \"用药后/限制级\"}], \"图片地址\": \"单张image_1547.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1547.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": 491012029253, \"姓名\": \"孙园芳\", \"年龄\": \"39\", \"性别\": \"女\", \"样本类型\": \"分泌物\", \"日期\": \"25年7月13日\", \"采集时间\": \"11:00\", \"医生\": NaN, \"科室\": NaN, \"检测项目\": \"HPV25\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1548.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1548.jpg"]}, {"messages": [{"role": "user", "content": "<image>\n你是一名OCR信息抽取助手。请对任意版式的送检单图片进行结构化识别，输出标准化的JSON格式结果。\n要求如下：\n1.仅提取下面10个固定字段的值，若某字段在图中缺失则返回空字符串\"\"，不要编造。\n 输出格式示例：\n{\n \"患者信息\": [\n {\n \"条形码\": \"\",\n \"姓名\": \"\",\n \"年龄\": \"\",\n \"性别\": \"\",\n \"样本类型\": \"\",\n \"日期\": \"\",\n \"采集时间\": \"\",\n \"送检医生\": \"\",\n \"检测项目\": \"\",\n \"备注栏\": \"\"\n }\n ]\n}\n只输出结构化后的JSON内容，不要输出多余解释。\n2. 输出必须为合法 JSON，键名与上面完全一致，大小写敏感，不要添加其他键。\n3. 表格类内容处理：\n - 保持原始表格的行列顺序和层级结构\n - 确保每个单元格独立识别，不合并相邻单元格\n  - 手动检查表格确保所有数据行都被正确识别。\n - 确保不遗漏任何未划除行数据\n5. 删除线处理：\n - 自动屏蔽所有被划掉内容（包括：单划线、双横线贯穿文字、波浪线、圆圈框选，涂黑块、带圈删除符号等），不要输出被删除的内容\n - 表格中被横线、双横线、删除线、斜线、×线等明显划掉的整行，整行不识别和输出（包含多个字段被划掉）\n 4. 手写体识别：\n - 启用增强型手写识别模块\n - 对模糊字迹进行三次交叉验证\n - 多尺度特征融合检测\n - 笔画拓扑结构分析\n5. 选择类信息处理：\n - 判断检测项目、样本类型\n - 有效勾选符号包括：√、☑、✓等\n - 符号位于选项框或标签边界内即视为选中\n - 无勾选符号或样本类型其他选项或检测项目其他选项横线上无文字描述则输出为null\n6. 选择类信息画勾与选项标签关联规则：\n - 画勾中心点位于选项选择框和标签边界内\n - 画勾与选项选择框和标签存在≥30%的像素重叠区域\n - 画勾形状和方向明显指向选项标签\n - 多种规则满足时以中心点规则为准\n - 如果其他选项附带文字说明时，输出格式：说明文字，示例：■其他:_分泌物 → 分泌物，如果横线上画勾就输出样本类型判定为'其他'。\n7. - 同一选项组出现多个勾选时，按空间顺序输出,示例：样本类型同时勾选□血清□全血 → ['血清+全血']。\n8. 数字识别：\n - 区分形状相似数字（1/4/7、0/6/8、0/9、3/5等）\n - 结合整体书写风格确保准确识别\n - 1/7：检查顶部横杆（1无横杆，7有横杆）\n - 4/7：检查有无交叉点（4有交叉点，7无交叉点）\n - 3/5：检查底部弧度（3平直，5弯曲）\n11. 汉字完整性原则：\n - 将每个汉字作为完整整体识别\n - 禁止拆分、禁止将汉字拆分成偏旁部首或组件。（如'斌'字绝不能拆为'文+武'）\n - 严格区分中文数字与阿拉伯数字（'一'与'1'、'一二三'与'123'视为不同字段）\n - 严格区分英文大小写。\n"}, {"role": "assistant", "content": "{\"患者信息\": [{\"条形码\": \"4Z0953007560\", \"姓名\": \"王秀平\", \"年龄\": \"58\", \"性别\": \"女\", \"样本类型\": \"血清\", \"日期\": \"2024年10月3日\", \"采集时间\": \"07:00\", \"医生\": \"赵金萍\", \"科室\": NaN, \"检测项目\": \"GM试验\", \"备注栏\": NaN}], \"图片地址\": \"单张image_1549.jpg\"}"}], "images": ["C:/core/DemoAI/dataset/images/image_1549.jpg"]}]
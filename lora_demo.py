import os
import sys
import torch
from torch.utils.data import DataLoader
from PIL import Image
from datasets import load_dataset
from peft import LoraConfig, get_peft_model
from transformers import (
    AutoModelForVision2Seq,
    AutoTokenizer,
    AutoProcessor,
    set_seed,
)

# --------------------------
# 0) 环境/线程控制（进一步省内存）
# --------------------------
os.environ.setdefault("OMP_NUM_THREADS", "1")
os.environ.setdefault("MKL_NUM_THREADS", "1")
torch.set_num_threads(1)

# --------------------------
# 1) 基础配置
# --------------------------
MODEL_NAME = "C:/Users/<USER>/.cache/modelscope/hub/models/Qwen/Qwen2-VL-2B-Instruct"
DATA_FILE  = "C:/core/DemoAI/dataset/medical_test_train.json"
OUTPUT_DIR = "./qwen-vl-lora-output"

BATCH_SIZE = 1
ACC_STEPS = 16
LR = 2e-4
EPOCHS = 3
MAX_LENGTH = 512
MAX_IMAGE_SIDE = 1024
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

set_seed(42)

# --------------------------
# 2) 构建输入文本
# --------------------------
def build_text_from_messages(messages):
    user_prompt, assistant_resp = "", ""
    for m in messages:
        role = m.get("role", "")
        content = m.get("content", "")
        if content is None or isinstance(content, float):
            content = ""
        if isinstance(content, str):
            content = content.replace("NaN", "")
        if role == "user":
            user_prompt = content
        elif role == "assistant":
            assistant_resp = content

    return (
        "<|im_start|>user\n<images>\n"
        + user_prompt
        + "<|im_end|>\n<|im_start|>assistant\n"
        + assistant_resp
        + "<|im_end|>"
    )

def to_minimal_fields(example):
    img_path = example["images"][0] if example.get("images") else None
    if not img_path or not os.path.exists(img_path):
        return None
    text = build_text_from_messages(example.get("messages", []))
    return {"text": text, "image_path": img_path}

# --------------------------
# 3) 模型/处理器
# --------------------------
def load_model_and_processor():
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME, trust_remote_code=True)
    processor = AutoProcessor.from_pretrained(MODEL_NAME, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    model = AutoModelForVision2Seq.from_pretrained(
        MODEL_NAME,
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        device_map=None,
        trust_remote_code=True
    )
    model.to(DEVICE)
    model.gradient_checkpointing_enable()
    return tokenizer, processor, model

def resize_long_side(img: Image.Image, max_side: int = 1024) -> Image.Image:
    w, h = img.size
    long_side = max(w, h)
    if long_side <= max_side:
        return img
    scale = max_side / float(long_side)
    new_w = max(1, int(w * scale))
    new_h = max(1, int(h * scale))
    return img.resize((new_w, new_h), Image.BILINEAR)

# --------------------------
# 4) Collator
# --------------------------
def make_collator(processor):
    def collate(features):
        texts = [f["text"] for f in features]
        pil_images = []
        for f in features:
            img = Image.open(f["image_path"]).convert("RGB")
            img = resize_long_side(img, MAX_IMAGE_SIDE)
            pil_images.append(img)

        batch = processor(
            text=texts,
            images=pil_images,
            padding="max_length",
            truncation=True,
            max_length=MAX_LENGTH,
            return_tensors="pt"
        )
        labels = batch["input_ids"].clone()
        labels[batch["attention_mask"] == 0] = -100
        batch["labels"] = labels
        return {k: v.to(DEVICE) for k, v in batch.items()}
    return collate

# --------------------------
# 5) PyTorch Training Loop
# --------------------------
def train_loop(model, dataloader, optimizer, scheduler=None):
    model.train()
    global_step = 0
    optimizer.zero_grad()

    for epoch in range(EPOCHS):
        for step, batch in enumerate(dataloader):
            outputs = model(**batch)
            loss = outputs.loss / ACC_STEPS
            loss.backward()

            if (step + 1) % ACC_STEPS == 0:
                optimizer.step()
                optimizer.zero_grad()
                if scheduler:
                    scheduler.step()

            if global_step % 10 == 0:
                print(f"Epoch {epoch} | Step {step} | Loss {loss.item()*ACC_STEPS:.4f}")

            global_step += 1

# --------------------------
# 6) Main
# --------------------------
def main():
    if not os.path.exists(MODEL_NAME):
        print(f"[FATAL] Model path does not exist: {MODEL_NAME}")
        sys.exit(1)
    if not os.path.exists(DATA_FILE):
        print(f"[FATAL] Dataset file not found: {DATA_FILE}")
        sys.exit(1)

    tokenizer, processor, model = load_model_and_processor()

    # LoRA 注入
    lora_config = LoraConfig(
        r=8,
        lora_alpha=16,
        target_modules=["q_proj", "v_proj"],
        lora_dropout=0.05,
        bias="none",
        task_type="CAUSAL_LM"
    )
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()

    # 数据集
    raw_ds = load_dataset("json", data_files=DATA_FILE)
    mini_ds = raw_ds.map(
        to_minimal_fields,
        remove_columns=raw_ds["train"].column_names,
        batched=False
    )
    mini_ds = mini_ds.filter(lambda x: x is not None and "text" in x and "image_path" in x)

    collator = make_collator(processor)
    dataloader = DataLoader(mini_ds["train"], batch_size=BATCH_SIZE, shuffle=True, collate_fn=collator)

    # Optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=LR)

    # Train
    train_loop(model, dataloader, optimizer)

    # 保存
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    model.save_pretrained(OUTPUT_DIR)
    processor.save_pretrained(OUTPUT_DIR)
    tokenizer.save_pretrained(OUTPUT_DIR)
    print("✅ LoRA 微调完成，权重已保存到", OUTPUT_DIR)

if __name__ == "__main__":
    main()

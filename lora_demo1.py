# train_qwen2vl_lora.py
# 微调 Qwen2-VL-2B-Instruct（OCR结构化抽取）— LoRA / QLoRA 训练脚本
# Author: you + GPT
# -*- coding: utf-8 -*-

import os
import json
import math
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, Tuple

import torch
from torch.utils.data import Dataset

from transformers import (
    AutoModelForVision2Seq,
    AutoProcessor,
    AutoTokenizer,
    Trainer,
    TrainingArguments,
    BitsAndBytesConfig,
    set_seed,
)

from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training

from PIL import Image

# ========== 配置区域 ==========
MODEL_NAME = "C:/Users/<USER>/.cache/modelscope/hub/models/Qwen/Qwen2-VL-2B-Instruct"   # 也可替换为本地路径
DATA_JSON = "C:/core/DemoAI/dataset/medical_test_train.json"      # 你的数据文件
IMAGE_ROOT = ""                            # 如果 images 路径不是绝对路径，可在这里统一加前缀（如"C:/core/DemoAI/dataset/"）
OUTPUT_DIR = "./qwen2vl_lora_medical_ocr"

SEED = 42
USE_QLORA = True  # True: 4bit 量化 (QLoRA), False: 全精度/半精度 LoRA
BATCH_SIZE = 2
GRAD_ACCUM = 8
LR = 2e-4
EPOCHS = 2
MAX_SEQ_LEN = 4096  # 你的显存若不足可以降低
IMAGE_MAX_RES = 1120  # 保持分辨率上限（Qwen2-VL推荐 448~1344 范围内根据显存调整）

# LoRA 超参
LORA_R = 16
LORA_ALPHA = 32
LORA_DROPOUT = 0.05
# 针对 Qwen2 系列常用的 LoRA 目标模块（注意包含投影/前馈）
LORA_TARGET_MODULES = [
    "q_proj", "k_proj", "v_proj", "o_proj",
    "gate_proj", "up_proj", "down_proj"
]

# 是否启用 bf16/fp16（自动检测环境）
BF16 = torch.cuda.is_available() and torch.cuda.get_device_capability(0)[0] >= 8
FP16 = not BF16 and torch.cuda.is_available()

# ========== 固定字段与清洗 ==========
TEN_KEYS = ["条形码","姓名","年龄","性别","样本类型","日期","采集时间","送检医生","检测项目","备注栏"]

# 某些样本里 assistant 返回了 "医生"、"科室"、NaN 等，需要统一与裁剪
def sanitize_assistant_json(raw_text: str) -> str:
    """
    输入可能是字符串化的 JSON；做：
    1) JSON parse，容错处理
    2) 只保留 TEN_KEYS，缺失补 ""
    3) 键名映射：“医生”->“送检医生”；丢弃“科室”等多余字段
    4) 统一把 NaN/None/null -> ""
    5) 最终返回严格合法 JSON 字符串
    """
    def _to_str(x):
        if x is None: return ""
        if isinstance(x, float):
            # 处理 NaN/Inf
            if math.isnan(x) or math.isinf(x): return ""
            return str(x)
        return "" if str(x).strip().lower() in ("nan","null","none") else str(x)

    # 宽松加载
    data = None
    try:
        data = json.loads(raw_text)
    except Exception:
        # 有些样本最外层还包含 "图片地址" 等，或者有转义问题，尝试清洗再 parse
        try:
            raw_text2 = raw_text.replace("NaN", "null")
            data = json.loads(raw_text2)
        except Exception:
            # 实在不行，返回空模板
            return json.dumps({"患者信息":[{k:"" for k in TEN_KEYS}]}, ensure_ascii=False)

    # 兼容最外层直接就是 {"患者信息":[{...}]}
    if not isinstance(data, dict) or "患者信息" not in data:
        # 尝试在 data 内寻找
        if isinstance(data, list) and len(data)>0 and isinstance(data[0], dict) and "患者信息" in data[0]:
            data = data[0]
        else:
            # 不规范，返回空模板
            return json.dumps({"患者信息":[{k:"" for k in TEN_KEYS}]}, ensure_ascii=False)

    arr = data.get("患者信息", [])
    if not isinstance(arr, list) or len(arr)==0 or not isinstance(arr[0], dict):
        return json.dumps({"患者信息":[{k:"" for k in TEN_KEYS}]}, ensure_ascii=False)
    item = arr[0]

    # 键名映射
    if "医生" in item and "送检医生" not in item:
        item["送检医生"] = item["医生"]
    # 统一并裁剪
    clean = {}
    for k in TEN_KEYS:
        clean[k] = _to_str(item.get(k, ""))

    return json.dumps({"患者信息":[clean]}, ensure_ascii=False)

# ========== 数据集 ==========
class VLChatDataset(Dataset):
    """
    读取 medical_test_train.json
    每条样本:
    {
      "messages":[{"role":"user","content":"<image> ..."}, {"role":"assistant","content":"{...json...}"}],
      "images":["path/to/img.jpg"]
    }
    """
    def __init__(self, path: str, processor: AutoProcessor, image_root: str = ""):
        super().__init__()
        with open(path, "r", encoding="utf-8") as f:
            raw = json.load(f)
        self.data = []
        self.processor = processor
        self.image_root = image_root

        for ex in raw:
            msgs = ex.get("messages", [])
            imgs = ex.get("images", [])
            if not msgs or not imgs:
                continue
            # 拿第一张图
            img_path = imgs[0]
            if self.image_root and not os.path.isabs(img_path):
                img_path = os.path.join(self.image_root, img_path)
            if not os.path.exists(img_path):
                # 可选择跳过或报错
                continue

            # user / assistant
            user_msg = None
            asst_msg = None
            for m in msgs:
                if m.get("role") == "user":
                    user_msg = m.get("content", "")
                elif m.get("role") == "assistant":
                    asst_msg = m.get("content", "")

            if not user_msg or not asst_msg:
                continue

            # 清洗 assistant 的 JSON
            asst_json = sanitize_assistant_json(asst_msg)

            self.data.append({
                "image_path": img_path,
                "user_text": user_msg,
                "assistant_text": asst_json
            })

        print(f"[Dataset] Loaded {len(self.data)} samples from {path}")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        # 加载图片
        image = Image.open(item["image_path"]).convert("RGB")

        # 组装 chat：Qwen2-VL 推荐使用 processor 的 chat 模板
        # content 要用多模态 list：[{type:"image"}, {type:"text", text:"..."}]
        chat = [
            {
                "role": "user",
                "content": [
                    {"type": "image"},
                    {"type": "text", "text": item["user_text"]},
                ]
            },
            {
                "role": "assistant",
                "content": [
                    {"type": "text", "text": item["assistant_text"]},
                ]
            }
        ]
        return {"chat": chat, "image": image}

# ========== Collator：构造成对的 input/labels，并只对 assistant 段落计 loss ==========
@dataclass
class DataCollatorForQwen2VL:
    processor: AutoProcessor
    max_length: int = MAX_SEQ_LEN
    image_max_res: int = IMAGE_MAX_RES

    def __call__(self, features: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        # 先把 chat 转成模板文本（含多模态占位）
        chats = [f["chat"] for f in features]
        images = [f["image"] for f in features]

        # apply_chat_template: 返回带 <|im_start|>user/assistant ... 的文本
        # 注意：这里 tokenize=False 先得模板文本；随后交由 processor 一次性处理图文
        texts = [
            self.processor.apply_chat_template(
                chat, tokenize=False, add_generation_prompt=False
            )
            for chat in chats
        ]

        # 多模态打包：一次性传入 texts 和 images
        batch = self.processor(
            text=texts,
            images=images,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=self.max_length,
            image_aspect_ratio="pad",
        )

        # 构造 labels，仅 assistant 区间有效，其它为 -100
        # 策略：在 token 序列中，找到最后一次出现的 "<|im_start|>assistant" 到对应 "<|im_end|>" 区间，打开监督
        # （如果有多个 assistant turn，你也可以累计多个区间；这里按单轮示例/或仅最后一轮）
        input_ids = batch["input_ids"]
        labels = input_ids.clone()

        # 取关键 token ids
        tok = self.processor.tokenizer
        # 这些 special token 在 Qwen2 系列中可用；不同版本名称可能略有差异
        im_start_id = tok.convert_tokens_to_ids("<|im_start|>")
        im_end_id = tok.convert_tokens_to_ids("<|im_end|>")

        # “assistant”这个词本身的 ids
        assistant_ids = tok.encode("assistant", add_special_tokens=False)

        for i in range(input_ids.size(0)):
            ids = input_ids[i].tolist()

            # Mask 全部
            mask = [-100] * len(ids)

            # 找到所有 "<|im_start|> assistant ... <|im_end|>" 片段
            j = 0
            while j < len(ids):
                # 查找一个 im_start
                try:
                    j = ids.index(im_start_id, j)
                except ValueError:
                    break
                # 期望后面紧跟 "assistant" 的字串
                k = j + 1
                ok = True
                for t in assistant_ids:
                    if k >= len(ids) or ids[k] != t:
                        ok = False
                        break
                    k += 1

                # 向后找 im_end
                try:
                    end_pos = ids.index(im_end_id, k)
                except ValueError:
                    end_pos = len(ids) - 1

                if ok:
                    # 仅 assistant 文本区间参与损失（不含起始 tokens）
                    for p in range(k, end_pos):
                        mask[p] = ids[p]

                j = end_pos + 1

            labels[i] = torch.tensor(mask, dtype=labels.dtype)

        batch["labels"] = labels
        return batch

# ========== 训练主流程 ==========
def main():
    set_seed(SEED)

    # 量化 / 精度
    quant_config = None
    torch_dtype = torch.bfloat16 if BF16 else (torch.float16 if FP16 else torch.float32)

    if USE_QLORA:
        quant_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch_dtype,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True
        )

    # 加载模型 & 处理器
    # Qwen2-VL 是 Vision2Seq 系列
    model = AutoModelForVision2Seq.from_pretrained(
        MODEL_NAME,
        torch_dtype=torch_dtype,
        quantization_config=quant_config if USE_QLORA else None,
        device_map="auto"
    )

    processor = AutoProcessor.from_pretrained(MODEL_NAME)
    # 保证 tokenizer 的 padding side/right 设置合理
    tokenizer: AutoTokenizer = processor.tokenizer
    tokenizer.padding_side = "right"
    tokenizer.truncation_side = "right"

    # 准备 LoRA
    if USE_QLORA:
        model = prepare_model_for_kbit_training(model)

    lora_cfg = LoraConfig(
        r=LORA_R,
        lora_alpha=LORA_ALPHA,
        target_modules=LORA_TARGET_MODULES,
        lora_dropout=LORA_DROPOUT,
        bias="none",
        task_type="CAUSAL_LM",
    )
    model = get_peft_model(model, lora_cfg)
    model.print_trainable_parameters()

    # 数据
    train_dataset = VLChatDataset(DATA_JSON, processor, IMAGE_ROOT)

    # Collator
    collator = DataCollatorForQwen2VL(processor=processor)

    # 训练参数
    args = TrainingArguments(
        output_dir=OUTPUT_DIR,
        num_train_epochs=EPOCHS,
        per_device_train_batch_size=BATCH_SIZE,
        gradient_accumulation_steps=GRAD_ACCUM,
        learning_rate=LR,
        optim="adamw_torch",
        lr_scheduler_type="cosine",
        warmup_ratio=0.03,
        logging_steps=10,
        save_steps=500,
        bf16=BF16,
        fp16=FP16,
        gradient_checkpointing=True,
        dataloader_num_workers=2,
        report_to="none",
        remove_unused_columns=False,  # 多模态必须保留
        max_grad_norm=1.0,
    )

    trainer = Trainer(
        model=model,
        args=args,
        train_dataset=train_dataset,
        eval_dataset=None,
        data_collator=collator,
        tokenizer=tokenizer,
    )

    # 训练
    trainer.train()

    # 保存 Adapter
    trainer.save_model()  # 保存 peft adapter 到 OUTPUT_DIR

    # （可选）把 LoRA 合并回基座权重供推理部署
    # 如果你需要合并，取消注释：
    # print("Merging LoRA weights into base model (optional)...")
    # merged = model.merge_and_unload()
    # merged.save_pretrained(os.path.join(OUTPUT_DIR, "merged"), safe_serialization=True)
    # processor.save_pretrained(os.path.join(OUTPUT_DIR, "merged"))

    # 同时保存处理器
    processor.save_pretrained(OUTPUT_DIR)

if __name__ == "__main__":
    main()

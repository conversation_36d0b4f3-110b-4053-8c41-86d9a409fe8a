import os
import json
import math

# 配置
INPUT_DIR = "./dataset"     # 原始数据集文件夹
OUTPUT_DIR = "./clean_dataset"  # 清洗后的输出文件夹
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 统一字段映射（例如 dataset 里有 content / text / prompt 混乱时）
FIELD_MAP = {
    "prompt": "instruction",
    "question": "instruction",
    "text": "instruction",
    "content": "instruction",
    "input": "input",
    "answer": "output",
    "response": "output",
    "output": "output"
}

def normalize_value(value):
    """修正非法值"""
    if value is None or isinstance(value, float) and (math.isnan(value) or math.isinf(value)):
        return ""
    if isinstance(value, str):
        return value.strip()
    return value

def normalize_item(item):
    """修正单个样本"""
    new_item = {"instruction": "", "input": "", "output": ""}
    for k, v in item.items():
        std_key = FIELD_MAP.get(k, None)
        if std_key:
            new_item[std_key] = normalize_value(v)
    # 确保三个字段必有
    for key in new_item:
        if key not in new_item or new_item[key] is None:
            new_item[key] = ""
    return new_item

def clean_json_file(file_path, output_path):
    """清洗单个 JSON 文件"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        # 如果是 dict，转为 list
        if isinstance(data, dict):
            data = [data]

        clean_data = [normalize_item(item) for item in data if isinstance(item, dict)]

        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(clean_data, f, ensure_ascii=False, indent=2)

        print(f"[OK] {file_path} -> {output_path} ({len(clean_data)} samples)")
    except Exception as e:
        print(f"[ERROR] {file_path}: {e}")

def batch_clean():
    for fname in os.listdir(INPUT_DIR):
        if fname.endswith(".json"):
            in_path = os.path.join(INPUT_DIR, fname)
            out_path = os.path.join(OUTPUT_DIR, fname)
            clean_json_file(in_path, out_path)

if __name__ == "__main__":
    batch_clean()
